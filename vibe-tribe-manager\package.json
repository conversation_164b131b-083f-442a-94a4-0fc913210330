{"name": "socialtribe-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "backend": "node start-backend.js", "backend:simple": "node simple-backend.cjs", "server": "node backend/dist/server.js", "server:dev": "tsx watch backend/src/server.ts", "build:backend": "tsc -p backend/tsconfig.json", "dev:full": "concurrently \"npm run backend\" \"npm run dev\"", "firebase:setup": "tsx backend/src/scripts/migrate.ts", "firebase:test": "tsx backend/src/scripts/test-firebase.ts", "test:auth": "tsx backend/src/scripts/test-auth.ts", "test:server": "tsx backend/src/test-server.ts", "db:setup": "npm run firebase:setup", "setup:oauth": "node scripts/setup-oauth.js", "setup:demo": "node -e \"require('./scripts/setup-oauth.js').setupDemoMode()\"", "setup:production": "node -e \"require('./scripts/setup-oauth.js').setupProductionMode()\"", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "deploy:netlify": "npm run build && npx netlify-cli deploy --prod --dir=dist", "generate-base64": "node scripts/generate-base64-credentials.js"}, "dependencies": {"@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.56.2", "@types/dompurify": "^3.0.5", "bcryptjs": "^3.0.2", "bull": "^4.16.3", "class-variance-authority": "^0.7.1", "cloudinary": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "dompurify": "^3.2.6", "dotenv": "^16.5.0", "embla-carousel-react": "^8.3.0", "emoji-picker-react": "^4.12.2", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "firebase": "^11.8.1", "firebase-admin": "^13.4.0", "helmet": "^8.1.0", "input-otp": "^1.2.4", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.462.0", "multer": "^1.4.5-lts.1", "next-themes": "^0.3.0", "node-cron": "^4.0.7", "node-fetch": "^3.3.2", "openai": "^4.103.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.53.0", "react-image-crop": "^11.0.10", "react-intersection-observer": "^9.16.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "redis": "^4.7.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.25.30"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/bcryptjs": "^2.4.6", "@types/bull": "^4.10.0", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^22.5.5", "@types/node-cron": "^3.0.11", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "autoprefixer": "^10.4.20", "concurrently": "^9.1.2", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "jsdom": "^23.0.1", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "tsx": "^4.19.4", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vitest": "^1.0.4"}}