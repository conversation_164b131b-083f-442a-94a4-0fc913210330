{"version": 3, "file": "oauth.d.ts", "sourceRoot": "", "sources": ["../../src/services/oauth.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AASrD,UAAU,WAAW;IACnB,OAAO,EAAE,OAAO,CAAC;IACjB,OAAO,CAAC,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;IACjC,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB;AAuED,qBAAa,mBAAmB;IAC9B,OAAO,CAAC,MAAM,CAAc;IAC5B,OAAO,CAAC,MAAM,CAAa;;IAoBrB,eAAe,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC;QAAE,GAAG,EAAE,MAAM,CAAC;QAAC,YAAY,EAAE,MAAM,CAAA;KAAE,CAAC;IA+B9E,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC;IAiExE,kBAAkB,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC;QAAE,WAAW,EAAE,MAAM,CAAC;QAAC,YAAY,CAAC,EAAE,MAAM,CAAA;KAAE,GAAG,IAAI,CAAC;CAc/G;AAGD,qBAAa,oBAAoB;IAC/B,OAAO,CAAC,MAAM,CAAc;;IAe5B,eAAe,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM;IAuBhC,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC;IA8ElD,oBAAoB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAuBhD,cAAc,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;CAcxD;AAGD,qBAAa,oBAAoB;IAC/B,OAAO,CAAC,MAAM,CAAc;;IAe5B,eAAe,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM;IAuBhC,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC;CAoEzD;AAGD,qBAAa,qBAAqB;IAChC,OAAO,CAAC,MAAM,CAAc;;IAe5B,eAAe,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM;IAuBhC,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC;CA0EzD;AAGD,qBAAa,mBAAmB;IAC9B,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM;IAelC,MAAM,CAAC,kBAAkB;IAIzB,MAAM,CAAC,iBAAiB;IAIxB,MAAM,CAAC,kBAAkB;IAIzB,MAAM,CAAC,mBAAmB;CAG3B"}